#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"
#include "Generated.xcconfig"

// Release configuration for GameFlex Mobile (Production)
PRODUCT_BUNDLE_IDENTIFIER = com.gameflex.app
BUNDLE_DISPLAY_NAME = GameFlex
BUNDLE_NAME = GameFlex

// Release-specific settings
FLUTTER_BUILD_MODE = release
DART_DEFINES = PRODUCTION=true

// Code signing (adjust as needed for your distribution team)
CODE_SIGN_IDENTITY = iPhone Distribution
DEVELOPMENT_TEAM =

// Deployment target
IPHONEOS_DEPLOYMENT_TARGET = 12.0

// Release optimizations
ENABLE_BITCODE = NO
VALIDATE_PRODUCT = YES
